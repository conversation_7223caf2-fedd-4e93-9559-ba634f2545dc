import { Routes, Route } from 'react-router-dom';
import Index from '@/pages/Index';
import About from '@/pages/About';
import Contact from '@/pages/Contact';
import Services from '@/pages/Services';
import ServicesSimple from '@/pages/ServicesSimple';
import TestPage from '@/pages/TestPage';
import Destinations from '@/pages/Destinations';
import TestPrep from '@/pages/TestPrep';
import Testimonials from '@/pages/Testimonials';
import NotFound from '@/pages/NotFound';
import Appointment from '@/pages/Appointment';
import Blog from '@/pages/Blog';
import BlogPost from '@/pages/BlogPost';
import StudentProgress from '@/pages/StudentProgress';
import UniversitySearch from '@/pages/UniversitySearch';
import CountryDestination from '@/pages/CountryDestination';
import { useAuth } from '@/contexts/AuthContext';
import AdminTestimonials from './pages/AdminTestimonials';
import AdminServices from './pages/AdminServices';
import AdminBlogPosts from './pages/AdminBlogPosts';
import AdminUniversities from './pages/AdminUniversities';
import AdminCountries from './pages/AdminCountries';
import AdminContacts from './pages/AdminContacts';
import AdminAppointments from './pages/AdminAppointments';
import AdminLogin from './components/admin/AdminLogin';
import ProtectedRoute from './components/admin/ProtectedRoute';
import EnhancedAdminDashboard from './pages/EnhancedAdminDashboard';
import AdminFileManager from './pages/AdminFileManager';
import AdminEmailTemplates from './pages/AdminEmailTemplates';
import DestinationSlugEditor from './pages/admin/destinations/DestinationSlugEditor';

const Router = () => {
  const { user } = useAuth();

  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/about" element={<About />} />
      <Route path="/services" element={<Services />} />
      <Route path="/destinations" element={<Destinations />} />
      <Route path="/test-prep" element={<TestPrep />} />
      <Route path="/testimonials" element={<Testimonials />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/appointment" element={<Appointment />} />
      <Route path="/blog" element={<Blog />} />
      <Route path="/blog/:slug" element={<BlogPost />} />
      <Route path="/destinations/:country_name_slug" element={<CountryDestination />} />

      {/* Admin Authentication */}
      <Route path="/admin/login" element={<AdminLogin />} />

      {/* Protected Admin Routes */}
      {/* Admin Destinations */}
      <Route path="/admin/destinations/:countryId/slug" element={
        <ProtectedRoute>
          <DestinationSlugEditor />
        </ProtectedRoute>
      } />

      <Route path="/admin" element={
        <ProtectedRoute>
          <EnhancedAdminDashboard />
        </ProtectedRoute>
      } />
      <Route path="/admin/dashboard" element={
        <ProtectedRoute>
          <EnhancedAdminDashboard />
        </ProtectedRoute>
      } />
      <Route path="/admin/testimonials" element={
        <ProtectedRoute>
          <AdminTestimonials />
        </ProtectedRoute>
      } />
      <Route path="/admin/services" element={
        <ProtectedRoute>
          <AdminServices />
        </ProtectedRoute>
      } />
      <Route path="/admin/blog-posts" element={
        <ProtectedRoute>
          <AdminBlogPosts />
        </ProtectedRoute>
      } />
      <Route path="/admin/universities" element={
        <ProtectedRoute>
          <AdminUniversities />
        </ProtectedRoute>
      } />
      <Route path="/admin/countries" element={
        <ProtectedRoute>
          <AdminCountries />
        </ProtectedRoute>
      } />
      <Route path="/admin/contacts" element={
        <ProtectedRoute>
          <AdminContacts />
        </ProtectedRoute>
      } />
      <Route path="/admin/appointments" element={
        <ProtectedRoute>
          <AdminAppointments />
        </ProtectedRoute>
      } />
      <Route path="/admin/files" element={
        <ProtectedRoute>
          <AdminFileManager />
        </ProtectedRoute>
      } />
      <Route path="/admin/email-templates" element={
        <ProtectedRoute>
          <AdminEmailTemplates />
        </ProtectedRoute>
      } />

      {/* Protected user routes */}
      {user && (
        <>
          <Route path="/student-progress" element={<StudentProgress />} />
          <Route path="/university-search" element={<UniversitySearch />} />
        </>
      )}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default Router;