import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export interface Service {
  id: string;
  created_at: string;
  title: string;
  description: string;
  icon_url: string;
  display_order: number;
  is_featured: boolean;
  color: string | null;
}

const useServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const { data, error } = await supabase
          .from('services')
          .select('*');

        if (error) {
          throw error;
        }

        setServices(data as Service[]);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return { services, loading, error };
};

export default useServices;