import { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { User, Session, AuthResponse, AuthTokenResponse } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { UserRole, PERMISSIONS } from '@/types/rbac';
import { initializeAdminUser, isUserAdmin } from '@/utils/init-admin';
import type { AuthContextType, UserSession } from './authTypes';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<UserRole | null>(null);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  type RefreshPermissionsFn = () => Promise<void>;
  const refreshPermissionsRef = useRef<RefreshPermissionsFn>(async () => {});

  // Define refreshPermissions with useCallback to avoid recreation on every render
  const refreshPermissions = useCallback(async (currentUser: User | null = user): Promise<void> => {
    if (!currentUser) {
      setPermissions([]);
      setRole(null);
      return;
    }

    setIsLoadingPermissions(true);

    try {
      // Get user role from profiles table
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', currentUser.id)
        .single();

      if (profileError) throw profileError;

      const userRole = profile?.role as UserRole;
      setRole(userRole);

      // Get permissions for the role
      const { data: permissionsData, error: permissionsError } = await supabase
        .from('role_permissions')
        .select('permission')
        .eq('role', userRole);

      if (permissionsError) throw permissionsError;

      const userPermissions = permissionsData?.map(p => p.permission) || [];
      setPermissions(userPermissions);
    } catch (error) {
      console.error('Error refreshing permissions:', error);
      setPermissions([]);
      setRole(null);
    } finally {
      setIsLoadingPermissions(false);
    }
  }, [user]);

  // Update the ref whenever refreshPermissions changes
  useEffect(() => {
    const refreshFn: RefreshPermissionsFn = async () => {
      if (!user) return;
      await refreshPermissions(user);
    };
    refreshPermissionsRef.current = refreshFn;
  }, [refreshPermissions, user]);

  // Fetch user profile with role and permissions
  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      // Get user profile with role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      const userRole = (profile?.role as UserRole) || null;
      setRole(userRole);

      // Get user permissions
      const { data: permissionsData, error: permError } = await supabase
        .rpc('get_user_permissions', { user_id: userId });

      if (permError) throw permError;

      const userPermissions = Array.isArray(permissionsData)
        ? permissionsData.map((p: { permission_name: string }) => p.permission_name)
        : [];

      setPermissions(userPermissions);

      return { role: userRole, permissions: userPermissions };
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setRole(null);
      setPermissions([]);
      return { role: null, permissions: [] };
    }
  }, []);

  // Initial auth setup
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true);
        const { data: { session: currentSession } } = await supabase.auth.getSession();

        if (currentSession?.user) {
          setUser(currentSession.user);
          setSession(currentSession);
          await fetchUserProfile(currentSession.user.id);
        } else {
          setUser(null);
          setSession(null);
          setRole(null);
          setPermissions([]);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
        setIsInitialized(true);
      }
    };

    initializeAuth();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchUserProfile(session.user.id);
      } else {
        setRole(null);
        setPermissions([]);
      }

      setLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [fetchUserProfile]);

  // Sign in a user
  const signIn = useCallback(async (email: string, password: string, rememberMe = false) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Get user profile with role
      let role: UserRole | null = null;
      if (data.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', data.user.id)
          .single();

        if (profileError) throw profileError;

        // Set user role and permissions
        role = profileData?.role || null;
        setRole(role);

        // In a real app, you would fetch permissions from your database
        // For now, we'll use a default set of permissions
        const defaultPermissions = ['read:profile', 'update:profile'];
        setPermissions(defaultPermissions);
      }

      return {
        data: { user: data.user, session: data.session },
        error: null
      };
    } catch (error) {
      console.error('Error signing in:', error);
      return {
        data: { user: null, session: null },
        error: error instanceof Error ? error : new Error('An unknown error occurred')
      };
    } finally {
      setLoading(false);
    }
  }, []);

  // Sign up a new user
  const signUp = useCallback(async (email: string, password: string, fullName: string, isAdmin: boolean = false) => {
    setLoading(true);
    try {
      // Only allow admin creation if there are no users yet or if user is admin
      if (isAdmin) {
        const { count } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        if (count && count > 0) {
          const isCurrentUserAdmin = user && await isUserAdmin(user.id);
          if (!isCurrentUserAdmin) {
            throw new Error('Only administrators can create admin accounts');
          }
        }
      }

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) throw error;

      // Create user profile
      if (data.user) {
        const role = isAdmin ? 'admin' : 'user';

        await supabase.from('profiles').upsert({
          id: data.user.id,
          email: email.toLowerCase(),
          full_name: fullName,
          role,
          updated_at: new Date().toISOString(),
        });

        // If this is the first user and an admin, ensure they have all permissions
        if (isAdmin) {
          await initializeAdminUser(data.user.id, email);
        }

        // Refresh permissions for the current user
        if (user?.id === data.user.id) {
          await refreshPermissions();
        }
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error signing up:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to sign up')
      };
    } finally {
      setLoading(false);
    }
  }, [user, refreshPermissions]);

  const signOut = useCallback(async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      setUser(null);
      setSession(null);
      setRole(null);
      setPermissions([]);
      return { error };
    } catch (error) {
      console.error('Error signing out:', error);
      return {
        error: error instanceof Error ? error : new Error('An unknown error occurred')
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshSession = useCallback(async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      return {
        data: { session: data.session },
        error: null
      };
    } catch (error) {
      console.error('Error refreshing session:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to refresh session')
      };
    } finally {
      setLoading(false);
    }
  }, []);

  // Check if user has a specific role
  const hasRole = useCallback((checkRole: UserRole): boolean => {
    return role === checkRole;
  }, [role]);

  // Check if user has any of the specified roles
  const hasAnyRole = useCallback((roles: UserRole[]): boolean => {
    if (!role) return false;
    return roles.includes(role);
  }, [role]);

  // Check if user has a specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user || !permission) return false;
    return permissions.includes(permission);
  }, [user, permissions]);

  // Check if user has any of the specified permissions
  const hasAnyPermission = useCallback((checkPermissions: string[]): boolean => {
    if (!user || !checkPermissions?.length) return false;
    return checkPermissions.some(permission => permissions.includes(permission));
  }, [user, permissions]);

  // Check if user has all of the specified permissions
  const hasAllPermissions = useCallback((checkPermissions: string[]): boolean => {
    if (!user || !checkPermissions?.length) return false;
    return checkPermissions.every(permission => permissions.includes(permission));
  }, [user, permissions]);

  // Session management
  const getActiveSessions = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('user_id', user.id)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const revokeSession = async (sessionId: string) => {
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('user_sessions')
      .delete()
      .eq('id', sessionId)
      .eq('user_id', user.id);

    if (error) throw error;

    // If revoking current session, sign out
    if (sessionId === session?.user?.id) {
      await signOut();
    }
  };

  const revokeAllSessions = async () => {
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase.rpc('revoke_all_sessions', {
      p_user_id: user.id
    });

    if (error) throw error;

    // Sign out after revoking all sessions
    await signOut();
  };

  const value: AuthContextType = {
    // User and session
    user,
    session,
    loading: loading || !isInitialized,
    isAuthenticated: !!user,

    // Role and permissions
    role,
    permissions,
    isLoadingPermissions,

    // Auth methods
    signIn,
    signUp,
    signOut,
    refreshSession,

    // RBAC methods
    hasRole,
    hasAnyRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshPermissions,

    // Session management
    getActiveSessions,
    revokeSession,
    revokeAllSessions,
  } as const;

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

// Export the context for direct usage if needed
export const AuthContextConsumer = AuthContext.Consumer;

// Export the context and provider
export default AuthContext;