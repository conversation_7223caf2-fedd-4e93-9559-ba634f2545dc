
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import {
  GraduationCap,
  DollarSign,
  CreditCard,
  FileText,
  BookOpen,
  Home,
  Plane,
  Users,
  CheckCircle,
  ArrowRight,
  Star,
  Clock,
  Calendar,
  Search,
  Filter,
  GitCompare,
  Calculator,
  Heart,
  Share2,
  Download,
  Play,
  Award,
  Target,
  Zap,
  Shield,
  Globe,
  Phone,
  Mail,
  MessageCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Minus,
  X
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import useServices from '@/hooks/useServices';

// Enhanced service data with pricing and features
const enhancedServices = [
  {
    id: '1',
    title: 'University Selection & Admission',
    description: 'Expert guidance in choosing the right university and comprehensive application support.',
    shortDesc: 'University selection and admission guidance',
    icon: GraduationCap,
    color: 'from-blue-500 to-blue-600',
    price: { basic: 299, premium: 599, enterprise: 999 },
    duration: '2-4 weeks',
    features: ['University research', 'Application assistance', 'Document review', 'Interview prep'],
    rating: 4.9,
    reviews: 156,
    category: 'admission',
    popular: true
  },
  {
    id: '2',
    title: 'Visa Assistance & Documentation',
    description: 'Professional help with visa applications, documentation, and interview preparation.',
    shortDesc: 'Complete visa application support',
    icon: Shield,
    color: 'from-green-500 to-green-600',
    price: { basic: 199, premium: 399, enterprise: 699 },
    duration: '1-3 weeks',
    features: ['Visa application', 'Document preparation', 'Interview coaching', 'Status tracking'],
    rating: 4.8,
    reviews: 203,
    category: 'visa'
  },
  {
    id: '3',
    title: 'Scholarship & Financial Aid',
    description: 'Guidance in finding and applying for scholarships, grants, and financial assistance.',
    shortDesc: 'Scholarship search and application',
    icon: Award,
    color: 'from-yellow-500 to-orange-500',
    price: { basic: 149, premium: 299, enterprise: 499 },
    duration: '1-2 weeks',
    features: ['Scholarship search', 'Application writing', 'Financial planning', 'Award tracking'],
    rating: 4.7,
    reviews: 89,
    category: 'financial'
  },
  {
    id: '4',
    title: 'Test Preparation & Coaching',
    description: 'Comprehensive preparation for IELTS, TOEFL, GRE, GMAT, and other standardized tests.',
    shortDesc: 'Standardized test preparation',
    icon: Target,
    color: 'from-purple-500 to-purple-600',
    price: { basic: 199, premium: 399, enterprise: 699 },
    duration: '4-12 weeks',
    features: ['Practice tests', 'Study materials', 'One-on-one coaching', 'Score guarantee'],
    rating: 4.9,
    reviews: 312,
    category: 'preparation'
  },
  {
    id: '5',
    title: 'Career Counseling & Guidance',
    description: 'Personalized career guidance to help you make informed decisions about your future.',
    shortDesc: 'Professional career counseling',
    icon: Users,
    color: 'from-indigo-500 to-indigo-600',
    price: { basic: 99, premium: 199, enterprise: 349 },
    duration: '1-2 weeks',
    features: ['Career assessment', 'Industry insights', 'Goal setting', 'Action planning'],
    rating: 4.6,
    reviews: 127,
    category: 'counseling'
  },
  {
    id: '6',
    title: 'Pre-departure & Post-arrival Support',
    description: 'Essential support before departure and assistance with settling in your destination.',
    shortDesc: 'Complete departure and arrival support',
    icon: Plane,
    color: 'from-teal-500 to-teal-600',
    price: { basic: 149, premium: 299, enterprise: 499 },
    duration: '2-4 weeks',
    features: ['Pre-departure briefing', 'Accommodation help', 'Airport pickup', 'Settling assistance'],
    rating: 4.8,
    reviews: 94,
    category: 'support'
  }
];

// FAQ data
const faqData = [
  {
    category: 'General',
    questions: [
      {
        question: 'How long does the university application process take?',
        answer: 'The university application process typically takes 2-4 weeks, depending on the complexity of your profile and the number of universities you\'re applying to.'
      },
      {
        question: 'Do you provide support for all countries?',
        answer: 'Yes, we provide support for studying in over 25 countries including USA, UK, Canada, Australia, Germany, and many more.'
      }
    ]
  },
  {
    category: 'Pricing',
    questions: [
      {
        question: 'Are there any hidden fees?',
        answer: 'No, we believe in transparent pricing. All costs are clearly outlined in your service package with no hidden fees.'
      },
      {
        question: 'Do you offer payment plans?',
        answer: 'Yes, we offer flexible payment plans to make our services accessible. Contact us to discuss payment options.'
      }
    ]
  },
  {
    category: 'Services',
    questions: [
      {
        question: 'What is included in the premium package?',
        answer: 'Premium packages include priority support, additional consultations, document reviews, and enhanced guidance throughout the process.'
      },
      {
        question: 'Can I upgrade my service package later?',
        answer: 'Yes, you can upgrade your service package at any time. We\'ll adjust the pricing based on services already provided.'
      }
    ]
  }
];

// Testimonials data
const serviceTestimonials = [
  {
    id: 1,
    name: 'Sarah Johnson',
    university: 'Harvard University',
    service: 'University Selection & Admission',
    rating: 5,
    text: 'INNOVA helped me get into my dream university. Their guidance was invaluable throughout the entire process.',
    image: '/api/placeholder/60/60'
  },
  {
    id: 2,
    name: 'Michael Chen',
    university: 'University of Toronto',
    service: 'Visa Assistance',
    rating: 5,
    text: 'The visa process seemed overwhelming, but INNOVA made it smooth and stress-free. Highly recommended!',
    image: '/api/placeholder/60/60'
  },
  {
    id: 3,
    name: 'Emily Rodriguez',
    university: 'Oxford University',
    service: 'Scholarship Assistance',
    rating: 5,
    text: 'Thanks to INNOVA, I received a full scholarship. Their expertise in finding funding opportunities is amazing.',
    image: '/api/placeholder/60/60'
  }
];

const Services = () => {
  const { services, loading, error } = useServices();
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showComparison, setShowComparison] = useState(false);
  const [showPriceCalculator, setShowPriceCalculator] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'premium' | 'enterprise'>('basic');
  const [faqSearch, setFaqSearch] = useState('');
  const [openFaqItems, setOpenFaqItems] = useState<string[]>([]);
  const [favorites, setFavorites] = useState<string[]>([]);

  // Filter services based on search, category, and price
  const filteredServices = enhancedServices.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesPrice = service.price[selectedPlan] >= priceRange[0] && service.price[selectedPlan] <= priceRange[1];
    return matchesSearch && matchesCategory && matchesPrice;
  });

  // Filter FAQs based on search
  const filteredFAQs = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(q =>
      q.question.toLowerCase().includes(faqSearch.toLowerCase()) ||
      q.answer.toLowerCase().includes(faqSearch.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  // Calculate total price for selected services
  const calculateTotalPrice = () => {
    return selectedServices.reduce((total, serviceId) => {
      const service = enhancedServices.find(s => s.id === serviceId);
      return total + (service ? service.price[selectedPlan] : 0);
    }, 0);
  };

  // Toggle service selection for comparison
  const toggleServiceSelection = (serviceId: string) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : prev.length < 3 ? [...prev, serviceId] : prev
    );
  };

  // Toggle FAQ item
  const toggleFaqItem = (itemId: string) => {
    setOpenFaqItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // Toggle favorites
  const toggleFavorite = (serviceId: string) => {
    setFavorites(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  if (loading) return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  );

  if (error) return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="text-red-500 text-xl mb-4">Error loading services</div>
        <p className="text-gray-600">{error.message}</p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Enhanced Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white py-24 overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <motion.div
              className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <motion.div
              className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-orange-400/20 to-pink-600/20 rounded-full blur-3xl"
              animate={{
                scale: [1.2, 1, 1.2],
                rotate: [360, 180, 0],
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-6 border border-white/30"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                <Zap className="h-4 w-4 mr-2" />
                Interactive Service Explorer
              </motion.div>

              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                Transform Your
                <span className="block bg-gradient-to-r from-yellow-300 via-orange-300 to-pink-300 bg-clip-text text-transparent">
                  Study Abroad Dreams
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto mb-8 leading-relaxed">
                Discover our comprehensive suite of services designed to make your international education journey seamless and successful.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/contact"
                    className="bg-white text-blue-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg inline-flex items-center"
                  >
                    <Calendar className="mr-2 h-5 w-5" />
                    Book Free Consultation
                  </Link>
                </motion.div>
                <motion.button
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300 inline-flex items-center"
                  onClick={() => setShowPriceCalculator(true)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Calculator className="mr-2 h-5 w-5" />
                  Price Calculator
                </motion.button>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                {[
                  { label: 'Services', value: '6+', icon: Target },
                  { label: 'Success Rate', value: '98%', icon: Award },
                  { label: 'Countries', value: '25+', icon: Globe },
                  { label: 'Students Helped', value: '1000+', icon: Users }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    className="text-center p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 + index * 0.1, duration: 0.6 }}
                  >
                    <stat.icon className="h-6 w-6 mx-auto mb-2 text-yellow-300" />
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <div className="text-sm text-white/80">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Interactive Services Explorer */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header with Controls */}
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  Interactive Service Explorer
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                  Discover, compare, and customize our comprehensive study abroad services to match your unique needs.
                </p>
              </motion.div>

              {/* Search and Filter Controls */}
              <div className="flex flex-col lg:flex-row gap-4 items-center justify-center mb-8">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search services..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-3 w-full"
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('all')}
                    size="sm"
                  >
                    All Services
                  </Button>
                  {['admission', 'visa', 'financial', 'preparation'].map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      onClick={() => setSelectedCategory(category)}
                      size="sm"
                      className="capitalize"
                    >
                      {category}
                    </Button>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowComparison(!showComparison)}
                    className="flex items-center gap-2"
                  >
                    <GitCompare className="h-4 w-4" />
                    Compare ({selectedServices.length})
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowPriceCalculator(!showPriceCalculator)}
                    className="flex items-center gap-2"
                  >
                    <Calculator className="h-4 w-4" />
                    Calculator
                  </Button>
                </div>
              </div>

              {/* Plan Selection */}
              <Tabs value={selectedPlan} onValueChange={(value) => setSelectedPlan(value as any)} className="mb-8">
                <TabsList className="grid w-full max-w-md mx-auto grid-cols-3">
                  <TabsTrigger value="basic">Basic</TabsTrigger>
                  <TabsTrigger value="premium">Premium</TabsTrigger>
                  <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Enhanced Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {filteredServices.map((service, index) => {
                const IconComponent = service.icon;
                const isSelected = selectedServices.includes(service.id);
                const isFavorite = favorites.includes(service.id);

                return (
                  <motion.div
                    key={service.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -5 }}
                    className="group"
                  >
                    <Card className={`h-full transition-all duration-300 hover:shadow-xl border-2 ${
                      isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <CardHeader className="relative">
                        {/* Popular Badge */}
                        {service.popular && (
                          <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white">
                            Popular
                          </Badge>
                        )}

                        {/* Action Buttons */}
                        <div className="absolute top-4 right-4 flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleFavorite(service.id)}
                            className={`p-2 ${isFavorite ? 'text-red-500' : 'text-gray-400'}`}
                          >
                            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
                          </Button>
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleServiceSelection(service.id)}
                            className="w-5 h-5"
                          />
                        </div>

                        {/* Service Icon */}
                        <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                          <IconComponent className="h-8 w-8 text-white" />
                        </div>

                        <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {service.title}
                        </CardTitle>
                        <CardDescription className="text-gray-600">
                          {service.shortDesc}
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="space-y-4">
                        {/* Rating and Reviews */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < Math.floor(service.rating)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                            <span className="text-sm font-medium ml-1">{service.rating}</span>
                          </div>
                          <span className="text-sm text-gray-500">({service.reviews} reviews)</span>
                        </div>

                        {/* Price */}
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-2xl font-bold text-gray-900">
                              ${service.price[selectedPlan]}
                            </span>
                            <span className="text-gray-500 ml-1">/ service</span>
                          </div>
                          <Badge variant="secondary" className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {service.duration}
                          </Badge>
                        </div>

                        {/* Features */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-gray-900">Key Features:</h4>
                          <ul className="space-y-1">
                            {service.features.slice(0, 3).map((feature, i) => (
                              <li key={i} className="flex items-center gap-2 text-sm text-gray-600">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-4">
                          <Button className="flex-1" asChild>
                            <Link to="/contact">
                              <Calendar className="mr-2 h-4 w-4" />
                              Book Now
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm">
                            <ArrowRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Service Comparison Modal */}
        <AnimatePresence>
          {showComparison && selectedServices.length > 0 && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowComparison(false)}
            >
              <motion.div
                className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-auto"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-gray-900">Service Comparison</h3>
                    <Button variant="ghost" onClick={() => setShowComparison(false)}>
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                </div>

                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {selectedServices.map(serviceId => {
                      const service = enhancedServices.find(s => s.id === serviceId);
                      if (!service) return null;
                      const IconComponent = service.icon;

                      return (
                        <Card key={service.id} className="border-2 border-blue-200">
                          <CardHeader>
                            <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${service.color} flex items-center justify-center mb-3`}>
                              <IconComponent className="h-6 w-6 text-white" />
                            </div>
                            <CardTitle className="text-lg">{service.title}</CardTitle>
                            <div className="text-2xl font-bold text-blue-600">
                              ${service.price[selectedPlan]}
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-500" />
                                <span className="text-sm">{service.duration}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                <span className="text-sm">{service.rating} ({service.reviews} reviews)</span>
                              </div>
                              <div>
                                <h4 className="font-medium mb-2">Features:</h4>
                                <ul className="space-y-1">
                                  {service.features.map((feature, i) => (
                                    <li key={i} className="flex items-center gap-2 text-sm">
                                      <CheckCircle className="h-3 w-3 text-green-500" />
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-bold text-lg">Total Package Price</h4>
                        <p className="text-sm text-gray-600">Selected {selectedServices.length} services</p>
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-blue-600">
                          ${calculateTotalPrice()}
                        </div>
                        <Button className="mt-2" asChild>
                          <Link to="/contact">Book Package</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Price Calculator Modal */}
        <AnimatePresence>
          {showPriceCalculator && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowPriceCalculator(false)}
            >
              <motion.div
                className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-auto"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-2xl font-bold text-gray-900">Price Calculator</h3>
                    <Button variant="ghost" onClick={() => setShowPriceCalculator(false)}>
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                </div>

                <div className="p-6 space-y-6">
                  <div>
                    <h4 className="font-medium mb-4">Select Services:</h4>
                    <div className="space-y-3">
                      {enhancedServices.map(service => (
                        <div key={service.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Checkbox
                              checked={selectedServices.includes(service.id)}
                              onCheckedChange={() => toggleServiceSelection(service.id)}
                            />
                            <div>
                              <div className="font-medium">{service.title}</div>
                              <div className="text-sm text-gray-500">{service.shortDesc}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold">${service.price[selectedPlan]}</div>
                            <div className="text-xs text-gray-500">{selectedPlan} plan</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-lg font-medium">Total Estimated Cost:</span>
                      <span className="text-3xl font-bold text-blue-600">${calculateTotalPrice()}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-6">
                      {(['basic', 'premium', 'enterprise'] as const).map(plan => (
                        <Button
                          key={plan}
                          variant={selectedPlan === plan ? 'default' : 'outline'}
                          onClick={() => setSelectedPlan(plan)}
                          className="capitalize"
                        >
                          {plan}
                        </Button>
                      ))}
                    </div>

                    <div className="space-y-3">
                      <Button className="w-full" asChild>
                        <Link to="/contact">Get Detailed Quote</Link>
                      </Button>
                      <Button variant="outline" className="w-full">
                        <Download className="mr-2 h-4 w-4" />
                        Download Estimate
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Service Testimonials */}
        <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Success Stories
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Hear from students who achieved their dreams with our services
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {serviceTestimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-1 mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                        ))}
                      </div>

                      <blockquote className="text-gray-700 mb-6 italic">
                        "{testimonial.text}"
                      </blockquote>

                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900">{testimonial.name}</div>
                          <div className="text-sm text-gray-600">{testimonial.university}</div>
                          <div className="text-xs text-blue-600">{testimonial.service}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Find answers to common questions about our services
              </p>

              {/* FAQ Search */}
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search FAQs..."
                  value={faqSearch}
                  onChange={(e) => setFaqSearch(e.target.value)}
                  className="pl-10 pr-4 py-3"
                />
              </div>
            </motion.div>

            <div className="space-y-8">
              {filteredFAQs.map((category, categoryIndex) => (
                <div key={category.category}>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{category.category}</h3>
                  <div className="space-y-4">
                    {category.questions.map((faq, faqIndex) => {
                      const faqId = `${category.category}-${faqIndex}`;
                      const isOpen = openFaqItems.includes(faqId);

                      return (
                        <motion.div
                          key={faqIndex}
                          initial={{ opacity: 0, y: 10 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          transition={{ delay: (categoryIndex * 0.1) + (faqIndex * 0.05), duration: 0.4 }}
                          viewport={{ once: true }}
                        >
                          <Card className="border border-gray-200 hover:border-gray-300 transition-colors">
                            <CardHeader
                              className="cursor-pointer"
                              onClick={() => toggleFaqItem(faqId)}
                            >
                              <div className="flex items-center justify-between">
                                <h4 className="text-lg font-medium text-gray-900 text-left">
                                  {faq.question}
                                </h4>
                                {isOpen ? (
                                  <ChevronUp className="h-5 w-5 text-gray-500" />
                                ) : (
                                  <ChevronDown className="h-5 w-5 text-gray-500" />
                                )}
                              </div>
                            </CardHeader>
                            <AnimatePresence>
                              {isOpen && (
                                <motion.div
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: 'auto', opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.3 }}
                                >
                                  <CardContent className="pt-0">
                                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                                  </CardContent>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </Card>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Enhanced CTA Section */}
        <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
          {/* Background Animation */}
          <div className="absolute inset-0">
            <motion.div
              className="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>

          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Ready to Transform Your Future?
              </h2>
              <p className="text-xl md:text-2xl mb-8 text-white/90">
                Join thousands of successful students who achieved their dreams with INNOVA.
                Your international education journey starts here.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/contact"
                    className="bg-white text-blue-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg inline-flex items-center"
                  >
                    <Calendar className="mr-2 h-5 w-5" />
                    Book Free Consultation
                  </Link>
                </motion.div>
                <motion.button
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300 inline-flex items-center"
                  onClick={() => setShowPriceCalculator(true)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Calculator className="mr-2 h-5 w-5" />
                  Calculate Your Investment
                </motion.button>
              </div>

              <div className="mt-8 flex items-center justify-center gap-8 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>Free Consultation</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>No Hidden Fees</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-300" />
                  <span>Expert Guidance</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Services;
