import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export interface University {
  id: string;
  name: string;
  description: string;
  ranking: number | null;
  tuition_fee_range: string | null;
  application_deadline: string | null;
  is_featured: boolean;
  logo_url: string | null;
  website_url: string | null;
  country_name: string | null;
  flag_url: string | null;
  cover_image_url: string | null;
  slug: string | null;
  created_at: string;
}

export interface Country {
  id: string;
  name: string;
  flag_url: string | null;
  is_popular: boolean;
  description: string | null;
  cover_image_url: string | null;
}

export interface UniversityFilters {
  search: string;
  country: string;
  ranking: string;
  tuitionRange: string;
  featured: boolean;
}

export const useUniversities = (filters?: Partial<UniversityFilters>) => {
  const [universities, setUniversities] = useState<University[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    fetchUniversities();
    fetchCountries();
  }, [filters]);

  const fetchUniversities = async () => {
    try {
      setLoading(true);
      setError(null);

      // First get universities with country information
      let query = supabase
        .from('universities')
        .select(`
          id,
          name,
          description,
          ranking,
          tuition_fee_range,
          application_deadline,
          is_featured,
          logo_url,
          website_url,
          cover_image_url,
          slug,
          created_at,
          country_id
        `, { count: 'exact' });

      // Apply filters
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters?.featured) {
        query = query.eq('is_featured', true);
      }

      // Order by featured first, then by name
      query = query.order('is_featured', { ascending: false })
                   .order('name', { ascending: true });

      const { data: universitiesData, error: universitiesError, count } = await query;

      if (universitiesError) {
        throw universitiesError;
      }

      // Get countries data separately
      const { data: countriesData, error: countriesError } = await supabase
        .from('countries')
        .select('id, name, flag_url');

      if (countriesError) {
        throw countriesError;
      }

      // Create a map of country_id to country info
      const countryMap = new Map();
      countriesData?.forEach(country => {
        countryMap.set(country.id, country);
      });

      // Transform the data to include country information
      const transformedData = universitiesData?.map(university => {
        const country = countryMap.get(university.country_id);
        return {
          ...university,
          country_name: country?.name || null,
          flag_url: country?.flag_url || null,
        };
      }) || [];

      // Apply country filter after transformation
      let filteredData = transformedData;
      if (filters?.country && filters.country !== 'all') {
        filteredData = transformedData.filter(uni => uni.country_name === filters.country);
      }

      setUniversities(filteredData);
      setTotalCount(count || 0);
    } catch (err) {
      console.error('Error fetching universities:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch universities');
    } finally {
      setLoading(false);
    }
  };

  const fetchCountries = async () => {
    try {
      const { data, error: supabaseError } = await supabase
        .from('countries')
        .select('*')
        .order('is_popular', { ascending: false })
        .order('name', { ascending: true });

      if (supabaseError) {
        throw supabaseError;
      }

      setCountries(data || []);
    } catch (err) {
      console.error('Error fetching countries:', err);
    }
  };

  const getFeaturedUniversities = () => {
    return universities.filter(uni => uni.is_featured);
  };

  const getUniversitiesByCountry = (countryName: string) => {
    return universities.filter(uni => uni.country_name === countryName);
  };

  return {
    universities,
    countries,
    loading,
    error,
    totalCount,
    refetch: fetchUniversities,
    getFeaturedUniversities,
    getUniversitiesByCountry,
  };
};

export default useUniversities;