import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  LayoutDashboard,
  MessageSquare,
  Briefcase,
  BookOpen,
  GraduationCap,
  Globe,
  Mail,
  Calendar,
  Users,
  Settings,
  LogOut,
  Bell,
  Search,
  Menu,
  X,
  FileText,
  Upload,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const { toast } = useToast();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Signed out successfully",
        description: "You have been logged out of the admin panel.",
      });
      navigate('/admin/login');
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "There was a problem signing you out.",
        variant: "destructive",
      });
    }
  };

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
      current: location.pathname === '/admin'
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      current: location.pathname === '/admin/analytics'
    },
    {
      name: 'Content Management',
      href: '#',
      icon: FileText,
      children: [
        {
          name: 'Blog Posts',
          href: '/admin/blog-posts',
          current: location.pathname === '/admin/blog-posts'
        },
        {
          name: 'Testimonials',
          href: '/admin/testimonials',
          current: location.pathname === '/admin/testimonials'
        },
        {
          name: 'Services',
          href: '/admin/services',
          current: location.pathname === '/admin/services'
        }
      ]
    },
    {
      name: 'Academic Data',
      href: '#',
      icon: GraduationCap,
      children: [
        {
          name: 'Universities',
          href: '/admin/universities',
          current: location.pathname === '/admin/universities'
        },
        {
          name: 'Countries',
          href: '/admin/countries',
          current: location.pathname === '/admin/countries'
        }
      ]
    },
    {
      name: 'Communications',
      href: '#',
      icon: Mail,
      children: [
        {
          name: 'Contact Messages',
          href: '/admin/contacts',
          current: location.pathname === '/admin/contacts'
        },
        {
          name: 'Appointments',
          href: '/admin/appointments',
          current: location.pathname === '/admin/appointments'
        },
        {
          name: 'Email Templates',
          href: '/admin/email-templates',
          current: location.pathname === '/admin/email-templates'
        }
      ]
    },
    {
      name: 'File Manager',
      href: '/admin/files',
      icon: Upload,
      current: location.pathname === '/admin/files'
    },
    {
      name: 'User Management',
      href: '/admin/users',
      icon: Users,
      current: location.pathname === '/admin/users'
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: location.pathname === '/admin/settings'
    }
  ];

  const Sidebar = ({ mobile = false }) => (
    <div className={`${mobile ? 'fixed inset-0 z-50 lg:hidden' : 'hidden lg:flex lg:flex-col lg:w-72'} bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 shadow-2xl`}>
      {mobile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
      )}
      <div className={`${mobile ? 'relative flex flex-col w-72 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900' : 'flex flex-col flex-1'}`}>
        {/* Enhanced Logo */}
        <div className="flex items-center justify-between h-20 px-6 bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-blue-600 font-bold text-lg">I</span>
            </div>
            <div>
              <span className="text-white font-bold text-xl">INNOVA</span>
              <div className="text-blue-100 text-xs font-medium">Admin Panel</div>
            </div>
          </div>
          {mobile && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
              className="text-blue-100 hover:text-white hover:bg-blue-600/20 rounded-lg"
            >
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>

        {/* Enhanced Navigation */}
        <nav className="flex-1 px-6 py-6 space-y-3 overflow-y-auto scrollbar-thin">
          {navigationItems.map((item) => (
            <div key={item.name}>
              {item.children ? (
                <div className="space-y-2">
                  <div className="flex items-center px-4 py-3 text-sm font-semibold text-gray-200 bg-gray-800/50 rounded-xl">
                    <div className="p-2 bg-gray-700 rounded-lg mr-3">
                      <item.icon className="h-4 w-4 text-gray-300" />
                    </div>
                    {item.name}
                  </div>
                  <div className="ml-6 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.name}
                        to={child.href}
                        className={`block px-4 py-3 text-sm rounded-xl transition-all duration-200 hover-scale ${
                          child.current
                            ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg'
                            : 'text-gray-200 hover:bg-gray-700/50 hover:text-white'
                        }`}
                        onClick={() => mobile && setSidebarOpen(false)}
                      >
                        <div className="flex items-center">
                          <div className={`w-2 h-2 rounded-full mr-3 ${
                            child.current ? 'bg-white' : 'bg-gray-500'
                          }`}></div>
                          {child.name}
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              ) : (
                <Link
                  to={item.href}
                  className={`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 hover-scale ${
                    item.current
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg'
                      : 'text-gray-200 hover:bg-gray-700/50 hover:text-white'
                  }`}
                  onClick={() => mobile && setSidebarOpen(false)}
                >
                  <div className={`p-2 rounded-lg mr-3 ${
                    item.current ? 'bg-white/20' : 'bg-gray-700'
                  }`}>
                    <item.icon className={`h-4 w-4 ${
                      item.current ? 'text-white' : 'text-gray-300'
                    }`} />
                  </div>
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </nav>

        {/* Enhanced User info */}
        <div className="p-6 border-t border-gray-700/50">
          <div className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-xl hover:bg-gray-700/50 transition-colors">
            <Avatar className="h-12 w-12 ring-2 ring-blue-500/50">
              <AvatarImage src={user?.user_metadata?.avatar_url} />
              <AvatarFallback className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold">
                {user?.email?.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-white truncate">
                {user?.user_metadata?.full_name || 'Admin User'}
              </p>
              <p className="text-xs text-gray-400 truncate">
                {user?.email}
              </p>
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs text-green-400 font-medium">Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Desktop Sidebar */}
      <Sidebar />

      {/* Mobile Sidebar */}
      {sidebarOpen && <Sidebar mobile />}

      {/* Main Content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Enhanced Top Header */}
        <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden hover:bg-gray-100 rounded-xl p-2"
              >
                <Menu className="h-5 w-5" />
              </Button>

              <div className="hidden md:flex items-center space-x-3 bg-gray-50 rounded-xl px-4 py-2 min-w-80">
                <Search className="h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search anything..."
                  className="border-0 bg-transparent text-sm focus:outline-none focus:ring-0 flex-1 placeholder-gray-400"
                />
                <kbd className="hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400 bg-gray-200 rounded">
                  ⌘K
                </kbd>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Quick Actions */}
              <div className="hidden lg:flex items-center space-x-2">
                <Button variant="outline" size="sm" className="rounded-xl">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              </div>

              {/* Enhanced Notifications */}
              <Button variant="ghost" size="sm" className="relative hover:bg-gray-100 rounded-xl p-2">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-gradient-to-r from-red-500 to-red-600 text-white border-2 border-white">
                  3
                </Badge>
              </Button>

              {/* Enhanced User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-xl hover:bg-gray-100">
                    <Avatar className="h-9 w-9 ring-2 ring-blue-500/20">
                      <AvatarImage src={user?.user_metadata?.avatar_url} />
                      <AvatarFallback className="bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold">
                        {user?.email?.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64 p-2" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal p-3">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user?.user_metadata?.avatar_url} />
                        <AvatarFallback className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                          {user?.email?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-semibold leading-none">
                          {user?.user_metadata?.full_name || 'Admin User'}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user?.email}
                        </p>
                        <div className="flex items-center mt-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-xs text-green-600 font-medium">Online</span>
                        </div>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/admin/profile')} className="rounded-lg p-3">
                    <Users className="mr-3 h-4 w-4" />
                    <div>
                      <div className="font-medium">Profile</div>
                      <div className="text-xs text-gray-500">Manage your account</div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/admin/settings')} className="rounded-lg p-3">
                    <Settings className="mr-3 h-4 w-4" />
                    <div>
                      <div className="font-medium">Settings</div>
                      <div className="text-xs text-gray-500">Preferences & config</div>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut} className="rounded-lg p-3 text-red-600 focus:text-red-600">
                    <LogOut className="mr-3 h-4 w-4" />
                    <div>
                      <div className="font-medium">Log out</div>
                      <div className="text-xs text-gray-500">Sign out of your account</div>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Enhanced Main Content Area */}
        <main className="flex-1 overflow-y-auto bg-gradient-to-br from-gray-50 to-gray-100/50 p-6 scrollbar-thin">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;