import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '@/contexts/AppContext';
import { supabase } from '@/lib/supabase';
import { Session, User, AuthChangeEvent } from '@supabase/supabase-js';

type UserMetadata = {
  role?: string;
  full_name?: string;
  // Add other user metadata fields as needed
};

type AppUser = User & {
  user_metadata?: UserMetadata;
};

type SignUpData = {
  email: string;
  password: string;
  userData?: {
    full_name: string;
    // Add other user data fields as needed
  };
};

type UseAuthReturn = {
  user: AppUser | null;
  session: Session | null;
  isLoading: boolean;
  isAdmin: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  // Add other auth methods as needed
};

export const useAuth = (): UseAuthReturn => {
  const { user: appUser, addNotification } = useApp();
  const [user, setUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Check if user is admin
  const isAdmin = user?.user_metadata?.role === 'admin' || false;
  const isAuthenticated = !!user;

  // Handle authentication state changes
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        setIsLoading(true);
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
      } catch (error) {
        console.error('Error getting initial session:', error);
        addNotification('Error loading user session', 'error');
      } finally {
        setIsLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        setSession(currentSession);
        setUser(currentSession?.user ?? null);

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_IN':
            addNotification('Successfully signed in!', 'success');
            // Don't auto-redirect - let components handle their own navigation
            break;
          case 'SIGNED_OUT':
            addNotification('Successfully signed out', 'info');
            navigate('/');
            break;
          case 'PASSWORD_RECOVERY':
            navigate('/reset-password');
            break;
          case 'USER_UPDATED':
            addNotification('Profile updated successfully', 'success');
            break;
          default:
            break;
        }
      }
    );

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [addNotification, navigate]);

  // Sign in with email and password
  const signIn = useCallback(
    async (email: string, password: string) => {
      try {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) throw error;
      } catch (error) {
        console.error('Error signing in:', error);
        addNotification(
          error instanceof Error ? error.message : 'Failed to sign in',
          'error'
        );
        throw error;
      }
    },
    [addNotification]
  );

  // Sign up with email and password
  const signUp = useCallback(
    async (data: SignUpData) => {
      const { email, password, userData } = data;
      try {
        const { error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: userData.full_name,
              avatar_url: '',
            },
          },
        });

        if (error) throw error;

        addNotification(
          'Sign up successful! Please check your email to confirm your account.',
          'success'
        );
      } catch (error) {
        console.error('Error signing up:', error);
        addNotification(
          error instanceof Error ? error.message : 'Failed to create account',
          'error'
        );
        throw error;
      }
    },
    [addNotification]
  );

  // Sign out
  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Error signing out:', error);
      addNotification('Failed to sign out', 'error');
      throw error;
    }
  }, [addNotification]);

  // Reset password
  const resetPassword = useCallback(
    async (email: string) => {
      try {
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/update-password`,
        });

        if (error) throw error;

        addNotification(
          'Password reset link sent to your email',
          'success'
        );
      } catch (error) {
        console.error('Error resetting password:', error);
        addNotification(
          error instanceof Error ? error.message : 'Failed to send reset email',
          'error'
        );
        throw error;
      }
    },
    [addNotification]
  );

  // Update password
  const updatePassword = useCallback(
    async (newPassword: string) => {
      try {
        const { error } = await supabase.auth.updateUser({
          password: newPassword,
        });

        if (error) throw error;

        addNotification('Password updated successfully', 'success');
      } catch (error) {
        console.error('Error updating password:', error);
        addNotification(
          error instanceof Error ? error.message : 'Failed to update password',
          'error'
        );
        throw error;
      }
    },
    [addNotification]
  );

  // Update user profile
  const updateProfile = useCallback(
    async (updates: { full_name?: string; avatar_url?: string }) => {
      try {
        const { error } = await supabase.auth.updateUser({
          data: {
            ...user?.user_metadata,
            ...updates,
          },
        });

        if (error) throw error;

        addNotification('Profile updated successfully', 'success');
      } catch (error) {
        console.error('Error updating profile:', error);
        addNotification(
          error instanceof Error ? error.message : 'Failed to update profile',
          'error'
        );
        throw error;
      }
    },
    [user, addNotification]
  );

  // Subscribe to auth state changes
  const onAuthStateChange = useCallback(
    (callback: (event: AuthChangeEvent, session: Session | null) => void) => {
      const { data } = supabase.auth.onAuthStateChange((event, session) => {
        callback(event as AuthChangeEvent, session);
      });

      return () => {
        data.subscription.unsubscribe();
      };
    },
    []
  );

  return {
    user,
    session,
    isLoading,
    isAdmin: user?.user_metadata?.role === 'admin' || false,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signOut,
  };
};

export default useAuth;
