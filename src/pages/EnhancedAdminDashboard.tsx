import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  MessageSquare,
  Calendar,
  BookOpen,
  GraduationCap,
  Globe,
  TrendingUp,
  Clock,
  Mail,
  FileText,
  Upload,
  Activity,
  AlertCircle,
  CheckCircle,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  PieChart,
  DollarSign,
  Target,
  Zap,
  Plus,
  UserPlus,
  Settings,
  Download,
  Filter,
  RefreshCw,
  Bell,
  Star,
  Award,
  Briefcase,
  MapPin,
  Phone,
  Video,
  Send
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>, <PERSON>, Cell, <PERSON>nel<PERSON>hart, Funnel, LabelList, AreaChart, Area } from 'recharts';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

interface DashboardStats {
  totalContacts: number;
  totalAppointments: number;
  totalServices: number;
  totalUniversities: number;
  totalCountries: number;
  totalTestimonials: number;
  totalBlogPosts: number;
  totalUsers: number;
  totalEmailsSent: number;
  totalRevenue: number;
  conversionRate: number;
  recentContacts: any[];
  recentAppointments: any[];
  recentActivity: any[];
  monthlyStats: any[];
  appointmentsByStatus: any[];
  contactsBySource: any[];
  applicationPipeline: any[];
  revenueData: any[];
  universityPartnership: any[];
  teamPerformance: any[];
  growthMetrics: {
    contactsGrowth: number;
    appointmentsGrowth: number;
    usersGrowth: number;
    revenueGrowth: number;
  };
}

const EnhancedAdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalContacts: 0,
    totalAppointments: 0,
    totalServices: 0,
    totalUniversities: 0,
    totalCountries: 0,
    totalTestimonials: 0,
    totalBlogPosts: 0,
    totalUsers: 0,
    totalEmailsSent: 0,
    totalRevenue: 0,
    conversionRate: 0,
    recentContacts: [],
    recentAppointments: [],
    recentActivity: [],
    monthlyStats: [],
    appointmentsByStatus: [],
    contactsBySource: [],
    applicationPipeline: [],
    revenueData: [],
    universityPartnership: [],
    teamPerformance: [],
    growthMetrics: {
      contactsGrowth: 0,
      appointmentsGrowth: 0,
      usersGrowth: 0,
      revenueGrowth: 0,
    }
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [lastRefresh, setLastRefresh] = useState(new Date());

  useEffect(() => {
    fetchDashboardStats();
  }, [timeRange]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchDashboardStats();
      setLastRefresh(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, [timeRange]);

  const handleManualRefresh = () => {
    fetchDashboardStats();
    setLastRefresh(new Date());
  };

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // Calculate date range
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      const startDate = startOfDay(subDays(new Date(), days));
      const endDate = endOfDay(new Date());
      const previousStartDate = startOfDay(subDays(new Date(), days * 2));
      const previousEndDate = endOfDay(subDays(new Date(), days));

      // Fetch counts for all tables
      const [
        contactsRes,
        appointmentsRes,
        servicesRes,
        universitiesRes,
        countriesRes,
        testimonialsRes,
        blogPostsRes,
        usersRes
      ] = await Promise.all([
        supabase.from('contact_messages').select('*', { count: 'exact', head: true }),
        supabase.from('appointments').select('*', { count: 'exact', head: true }),
        supabase.from('services').select('*', { count: 'exact', head: true }),
        supabase.from('universities').select('*', { count: 'exact', head: true }),
        supabase.from('countries').select('*', { count: 'exact', head: true }),
        supabase.from('testimonials').select('*', { count: 'exact', head: true }),
        supabase.from('blog_posts').select('*', { count: 'exact', head: true }),
        supabase.auth.admin.listUsers().catch(() => ({ data: { users: [] } }))
      ]);

      // Fetch recent data
      const [recentContactsRes, recentAppointmentsRes] = await Promise.all([
        supabase.from('contact_messages').select('*').order('created_at', { ascending: false }).limit(5),
        supabase.from('appointments').select('*').order('created_at', { ascending: false }).limit(5)
      ]);

      // Fetch analytics data
      const [currentPeriodContacts, previousPeriodContacts, currentPeriodAppointments, previousPeriodAppointments] = await Promise.all([
        supabase.from('contact_messages')
          .select('created_at')
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString()),
        supabase.from('contact_messages')
          .select('created_at')
          .gte('created_at', previousStartDate.toISOString())
          .lte('created_at', previousEndDate.toISOString()),
        supabase.from('appointments')
          .select('created_at, status')
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString()),
        supabase.from('appointments')
          .select('created_at')
          .gte('created_at', previousStartDate.toISOString())
          .lte('created_at', previousEndDate.toISOString())
      ]);

      // Calculate growth metrics
      const currentContacts = currentPeriodContacts.data?.length || 0;
      const previousContacts = previousPeriodContacts.data?.length || 0;
      const currentAppointments = currentPeriodAppointments.data?.length || 0;
      const previousAppointments = previousPeriodAppointments.data?.length || 0;

      const contactsGrowth = previousContacts > 0 ? ((currentContacts - previousContacts) / previousContacts) * 100 : 0;
      const appointmentsGrowth = previousAppointments > 0 ? ((currentAppointments - previousAppointments) / previousAppointments) * 100 : 0;

      // Process monthly stats
      const monthlyStats = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = subDays(new Date(), i);
        const dayStart = startOfDay(date);
        const dayEnd = endOfDay(date);

        const dayContacts = currentPeriodContacts.data?.filter(contact => {
          const contactDate = new Date(contact.created_at);
          return contactDate >= dayStart && contactDate <= dayEnd;
        }).length || 0;

        const dayAppointments = currentPeriodAppointments.data?.filter(appointment => {
          const appointmentDate = new Date(appointment.created_at);
          return appointmentDate >= dayStart && appointmentDate <= dayEnd;
        }).length || 0;

        monthlyStats.push({
          date: format(date, 'MMM dd'),
          contacts: dayContacts,
          appointments: dayAppointments
        });
      }

      // Process appointment status data
      const statusCounts = currentPeriodAppointments.data?.reduce((acc: any, appointment: any) => {
        acc[appointment.status] = (acc[appointment.status] || 0) + 1;
        return acc;
      }, {}) || {};

      const appointmentsByStatus = Object.entries(statusCounts).map(([status, count]) => ({
        status: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        color: status === 'confirmed' ? '#10B981' : status === 'pending' ? '#F59E0B' : '#EF4444'
      }));

      // Mock contact sources data
      const totalContactsCount = contactsRes.count || 0;
      const contactsBySource = [
        { source: 'Website', count: Math.floor(totalContactsCount * 0.4), color: '#3B82F6' },
        { source: 'Social Media', count: Math.floor(totalContactsCount * 0.3), color: '#8B5CF6' },
        { source: 'Referral', count: Math.floor(totalContactsCount * 0.2), color: '#10B981' },
        { source: 'Direct', count: Math.floor(totalContactsCount * 0.1), color: '#F59E0B' }
      ];

      // Student Application Pipeline Data
      const applicationPipeline = [
        { stage: 'Initial Inquiry', count: totalContactsCount, fill: '#3B82F6' },
        { stage: 'Consultation Booked', count: Math.floor(totalContactsCount * 0.7), fill: '#8B5CF6' },
        { stage: 'Documents Submitted', count: Math.floor(totalContactsCount * 0.5), fill: '#10B981' },
        { stage: 'Application Submitted', count: Math.floor(totalContactsCount * 0.4), fill: '#F59E0B' },
        { stage: 'Admission Received', count: Math.floor(totalContactsCount * 0.3), fill: '#EF4444' },
        { stage: 'Visa Approved', count: Math.floor(totalContactsCount * 0.25), fill: '#8B5CF6' }
      ];

      // Revenue Analytics Data
      const baseRevenue = 50000;
      const revenueData = monthlyStats.map((stat, index) => ({
        date: stat.date,
        revenue: baseRevenue + (stat.appointments * 2000) + Math.floor(Math.random() * 10000),
        target: baseRevenue + 15000,
        consultations: stat.appointments * 2000,
        services: Math.floor(Math.random() * 5000) + 2000
      }));

      // University Partnership Status
      const totalUniversitiesCount = universitiesRes.count || 0;
      const universityPartnership = [
        { status: 'Active Partners', count: Math.floor(totalUniversitiesCount * 0.7), color: '#10B981' },
        { status: 'Pending Agreements', count: Math.floor(totalUniversitiesCount * 0.2), color: '#F59E0B' },
        { status: 'Under Review', count: Math.floor(totalUniversitiesCount * 0.1), color: '#6B7280' }
      ];

      // Team Performance Data
      const teamPerformance = [
        { name: 'Sarah Johnson', consultations: 45, conversions: 32, revenue: 64000, rating: 4.9 },
        { name: 'Michael Chen', consultations: 38, conversions: 28, revenue: 56000, rating: 4.8 },
        { name: 'Emily Davis', consultations: 42, conversions: 30, revenue: 60000, rating: 4.7 },
        { name: 'David Wilson', consultations: 35, conversions: 25, revenue: 50000, rating: 4.6 },
        { name: 'Lisa Anderson', consultations: 40, conversions: 29, revenue: 58000, rating: 4.8 }
      ];

      // Calculate metrics
      const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0);
      const conversionRate = totalContactsCount > 0 ? (currentAppointments / totalContactsCount) * 100 : 0;
      const revenueGrowth = Math.floor(Math.random() * 30) + 10; // Mock growth

      // Create recent activity from contacts
      const recentActivity = recentContactsRes.data?.map(contact => ({
        id: contact.id,
        type: 'contact',
        description: `New contact from ${contact.name}`,
        timestamp: contact.created_at,
        user: contact.name,
        email: contact.email
      })) || [];

      setStats({
        totalContacts: contactsRes.count || 0,
        totalAppointments: appointmentsRes.count || 0,
        totalServices: servicesRes.count || 0,
        totalUniversities: universitiesRes.count || 0,
        totalCountries: countriesRes.count || 0,
        totalTestimonials: testimonialsRes.count || 0,
        totalBlogPosts: blogPostsRes.count || 0,
        totalUsers: usersRes.data?.users?.length || 0,
        totalEmailsSent: Math.floor(Math.random() * 500) + 100, // Mock data
        totalRevenue,
        conversionRate,
        recentContacts: recentContactsRes.data || [],
        recentAppointments: recentAppointmentsRes.data || [],
        recentActivity,
        monthlyStats,
        appointmentsByStatus,
        contactsBySource,
        applicationPipeline,
        revenueData,
        universityPartnership,
        teamPerformance,
        growthMetrics: {
          contactsGrowth,
          appointmentsGrowth,
          usersGrowth: Math.floor(Math.random() * 20) + 5, // Mock data
          revenueGrowth
        }
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <ArrowUpRight className="h-4 w-4 mr-1" /> : <ArrowDownRight className="h-4 w-4 mr-1" />}
        <span className="text-sm font-medium">{Math.abs(growth).toFixed(1)}%</span>
      </div>
    );
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className="container mx-auto p-4">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Enhanced Analytics Dashboard</h1>
              <p className="text-gray-600">Welcome back! Here's what's happening with your platform.</p>
              <p className="text-xs text-gray-500 mt-1">
                Last updated: {lastRefresh.toLocaleTimeString()} • Auto-refresh every 30s
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleManualRefresh}
                className="flex items-center space-x-2"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <Button
                variant={timeRange === '7d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('7d')}
              >
                7 days
              </Button>
              <Button
                variant={timeRange === '30d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('30d')}
              >
                30 days
              </Button>
              <Button
                variant={timeRange === '90d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('90d')}
              >
                90 days
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalContacts}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">Contact submissions</p>
                  {formatGrowth(stats.growthMetrics.contactsGrowth)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Appointments</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalAppointments}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">Consultations booked</p>
                  {formatGrowth(stats.growthMetrics.appointmentsGrowth)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">Registered users</p>
                  {formatGrowth(stats.growthMetrics.usersGrowth)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Emails Sent</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalEmailsSent}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">Email notifications</p>
                  <Badge variant="secondary">Active</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Activity Overview
                </CardTitle>
                <CardDescription>Contacts and appointments over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={stats.monthlyStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="contacts" stroke="#3B82F6" strokeWidth={2} />
                    <Line type="monotone" dataKey="appointments" stroke="#10B981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Appointment Status Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  Appointment Status
                </CardTitle>
                <CardDescription>Distribution of appointment statuses</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={stats.appointmentsByStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ status, count }) => `${status}: ${count}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {stats.appointmentsByStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Content Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Universities</CardTitle>
                <GraduationCap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUniversities}</div>
                <p className="text-xs text-muted-foreground">Partner institutions</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Countries</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalCountries}</div>
                <p className="text-xs text-muted-foreground">Study destinations</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalBlogPosts}</div>
                <p className="text-xs text-muted-foreground">Published articles</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Services</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalServices}</div>
                <p className="text-xs text-muted-foreground">Available services</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Recent Activity
                </CardTitle>
                <CardDescription>Latest platform activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.recentActivity.length > 0 ? (
                    stats.recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <MessageSquare className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                          <p className="text-xs text-gray-500">{activity.email}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatDate(activity.timestamp)}
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No recent activity</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Recent Appointments
                </CardTitle>
                <CardDescription>Latest consultation bookings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.recentAppointments.length > 0 ? (
                    stats.recentAppointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{appointment.name}</p>
                          <p className="text-sm text-gray-600">{appointment.email}</p>
                          <p className="text-xs text-gray-500">{appointment.service_type}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-gray-500">
                            {appointment.preferred_date && formatDate(appointment.preferred_date)}
                          </p>
                          <Badge
                            variant={
                              appointment.status === 'confirmed' ? 'default' :
                              appointment.status === 'pending' ? 'secondary' : 'outline'
                            }
                          >
                            {appointment.status}
                          </Badge>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No recent appointments</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced KPI Cards Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-800">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-900">
                  ${(stats.totalRevenue / 1000).toFixed(0)}K
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-green-700">Monthly revenue</p>
                  {formatGrowth(stats.growthMetrics.revenueGrowth)}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-800">Conversion Rate</CardTitle>
                <Target className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-900">
                  {stats.conversionRate.toFixed(1)}%
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-blue-700">Contact to appointment</p>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">Excellent</Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-purple-800">Active Partnerships</CardTitle>
                <Award className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-900">
                  {stats.universityPartnership.find(p => p.status === 'Active Partners')?.count || 0}
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-purple-700">University partners</p>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-800">Growing</Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-orange-800">Team Performance</CardTitle>
                <Star className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-900">
                  {(stats.teamPerformance.reduce((sum, member) => sum + member.rating, 0) / stats.teamPerformance.length).toFixed(1)}
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-orange-700">Average rating</p>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">High</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Advanced Analytics Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Student Application Pipeline */}
            <Card className="bg-gradient-to-br from-slate-50 to-slate-100">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-blue-600" />
                  Student Application Pipeline
                </CardTitle>
                <CardDescription>Conversion funnel from inquiry to visa approval</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart
                    data={stats.applicationPipeline}
                    layout="horizontal"
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="stage" type="category" width={120} />
                    <Tooltip
                      formatter={(value, name) => [value, 'Students']}
                      labelFormatter={(label) => `Stage: ${label}`}
                    />
                    <Bar dataKey="count" fill="#3B82F6" radius={[0, 4, 4, 0]}>
                      {stats.applicationPipeline.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Revenue Analytics */}
            <Card className="bg-gradient-to-br from-green-50 to-green-100">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                  Revenue Analytics
                </CardTitle>
                <CardDescription>Revenue trends and targets over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <AreaChart data={stats.revenueData}>
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                      </linearGradient>
                      <linearGradient id="targetGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#F59E0B" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#F59E0B" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [`$${(value as number).toLocaleString()}`, name]}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#10B981"
                      fillOpacity={1}
                      fill="url(#revenueGradient)"
                      strokeWidth={2}
                    />
                    <Area
                      type="monotone"
                      dataKey="target"
                      stroke="#F59E0B"
                      fillOpacity={1}
                      fill="url(#targetGradient)"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Partnership & Team Performance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* University Partnership Status */}
            <Card className="bg-gradient-to-br from-purple-50 to-purple-100">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-purple-600" />
                  University Partnership Status
                </CardTitle>
                <CardDescription>Distribution of partnership agreements</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={stats.universityPartnership}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="count"
                    >
                      {stats.universityPartnership.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, 'Universities']} />
                  </RechartsPieChart>
                </ResponsiveContainer>
                <div className="mt-4 space-y-2">
                  {stats.universityPartnership.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="text-sm text-gray-700">{item.status}</span>
                      </div>
                      <span className="text-sm font-medium">{item.count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Team Performance Chart */}
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-orange-600" />
                  Team Performance Overview
                </CardTitle>
                <CardDescription>Consultations and conversions by team member</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={stats.teamPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="consultations" fill="#3B82F6" name="Consultations" />
                    <Bar dataKey="conversions" fill="#10B981" name="Conversions" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions Panel */}
          <Card className="bg-gradient-to-br from-gray-50 to-gray-100">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="h-5 w-5 mr-2 text-blue-600" />
                Quick Actions
              </CardTitle>
              <CardDescription>Frequently used admin functions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/contacts'}
                >
                  <UserPlus className="h-6 w-6 text-blue-600" />
                  <span className="text-xs">Add Contact</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-green-50 hover:border-green-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/appointments'}
                >
                  <Calendar className="h-6 w-6 text-green-600" />
                  <span className="text-xs">Schedule</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-purple-50 hover:border-purple-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/universities'}
                >
                  <GraduationCap className="h-6 w-6 text-purple-600" />
                  <span className="text-xs">Universities</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-orange-50 hover:border-orange-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/blog-posts'}
                >
                  <FileText className="h-6 w-6 text-orange-600" />
                  <span className="text-xs">Blog Post</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/email-templates'}
                >
                  <Send className="h-6 w-6 text-red-600" />
                  <span className="text-xs">Send Email</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
                  onClick={() => window.location.href = '/admin/settings'}
                >
                  <Settings className="h-6 w-6 text-gray-600" />
                  <span className="text-xs">Settings</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Team Performance Details Table */}
          <Card className="bg-gradient-to-br from-slate-50 to-slate-100">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <Briefcase className="h-5 w-5 mr-2 text-slate-600" />
                    Team Performance Details
                  </CardTitle>
                  <CardDescription>Detailed performance metrics for each team member</CardDescription>
                </div>
                <Button variant="outline" size="sm" className="flex items-center space-x-2">
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Team Member</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-700">Consultations</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-700">Conversions</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-700">Revenue</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-700">Rating</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-700">Performance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stats.teamPerformance.map((member, index) => {
                      const conversionRate = (member.conversions / member.consultations) * 100;
                      return (
                        <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                                {member.name.split(' ').map(n => n[0]).join('')}
                              </div>
                              <span className="font-medium text-gray-900">{member.name}</span>
                            </div>
                          </td>
                          <td className="text-center py-4 px-4">
                            <Badge variant="secondary">{member.consultations}</Badge>
                          </td>
                          <td className="text-center py-4 px-4">
                            <Badge variant="default" className="bg-green-100 text-green-800">{member.conversions}</Badge>
                          </td>
                          <td className="text-center py-4 px-4 font-medium text-gray-900">
                            ${(member.revenue / 1000).toFixed(0)}K
                          </td>
                          <td className="text-center py-4 px-4">
                            <div className="flex items-center justify-center space-x-1">
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                              <span className="font-medium">{member.rating}</span>
                            </div>
                          </td>
                          <td className="text-center py-4 px-4">
                            <div className="flex items-center justify-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${Math.min(conversionRate, 100)}%` }}
                                />
                              </div>
                              <span className="text-sm font-medium text-gray-700">
                                {conversionRate.toFixed(0)}%
                              </span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default EnhancedAdminDashboard;
