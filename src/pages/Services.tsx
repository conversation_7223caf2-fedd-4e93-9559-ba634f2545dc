import {
  GraduationCap,
  Shield,
  Award,
  Target,
  Users,
  Plane
} from 'lucide-react';

// Enhanced service data with pricing and features
const enhancedServices = [
  {
    id: '1',
    title: 'University Selection & Admission',
    description: 'Expert guidance in choosing the right university and comprehensive application support.',
    shortDesc: 'University selection and admission guidance',
    icon: GraduationCap,
    color: 'from-blue-500 to-blue-600',
    price: { basic: 299, premium: 599, enterprise: 999 },
    duration: '2-4 weeks',
    features: ['University research', 'Application assistance', 'Document review', 'Interview prep'],
    rating: 4.9,
    reviews: 156,
    category: 'admission',
    popular: true
  },
  {
    id: '2',
    title: 'Visa Assistance & Documentation',
    description: 'Professional help with visa applications, documentation, and interview preparation.',
    shortDesc: 'Complete visa application support',
    icon: Shield,
    color: 'from-green-500 to-green-600',
    price: { basic: 199, premium: 399, enterprise: 699 },
    duration: '1-3 weeks',
    features: ['Visa application', 'Document preparation', 'Interview coaching', 'Status tracking'],
    rating: 4.8,
    reviews: 203,
    category: 'visa'
  },
  {
    id: '3',
    title: 'Scholarship & Financial Aid',
    description: 'Guidance in finding and applying for scholarships, grants, and financial assistance.',
    shortDesc: 'Scholarship search and application',
    icon: Award,
    color: 'from-yellow-500 to-orange-500',
    price: { basic: 149, premium: 299, enterprise: 499 },
    duration: '1-2 weeks',
    features: ['Scholarship search', 'Application writing', 'Financial planning', 'Award tracking'],
    rating: 4.7,
    reviews: 89,
    category: 'financial'
  },
  {
    id: '4',
    title: 'Test Preparation & Coaching',
    description: 'Comprehensive preparation for IELTS, TOEFL, GRE, GMAT, and other standardized tests.',
    shortDesc: 'Standardized test preparation',
    icon: Target,
    color: 'from-purple-500 to-purple-600',
    price: { basic: 199, premium: 399, enterprise: 699 },
    duration: '4-12 weeks',
    features: ['Practice tests', 'Study materials', 'One-on-one coaching', 'Score guarantee'],
    rating: 4.9,
    reviews: 312,
    category: 'preparation'
  },
  {
    id: '5',
    title: 'Career Counseling & Guidance',
    description: 'Personalized career guidance to help you make informed decisions about your future.',
    shortDesc: 'Professional career counseling',
    icon: Users,
    color: 'from-indigo-500 to-indigo-600',
    price: { basic: 99, premium: 199, enterprise: 349 },
    duration: '1-2 weeks',
    features: ['Career assessment', 'Industry insights', 'Goal setting', 'Action planning'],
    rating: 4.6,
    reviews: 127,
    category: 'counseling'
  },
  {
    id: '6',
    title: 'Pre-departure & Post-arrival Support',
    description: 'Essential support before departure and assistance with settling in your destination.',
    shortDesc: 'Complete departure and arrival support',
    icon: Plane,
    color: 'from-teal-500 to-teal-600',
    price: { basic: 149, premium: 299, enterprise: 499 },
    duration: '2-4 weeks',
    features: ['Pre-departure briefing', 'Accommodation help', 'Airport pickup', 'Settling assistance'],
    rating: 4.8,
    reviews: 94,
    category: 'support'
  }
];

const Services = () => {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: 'lightblue', padding: '20px' }}>
      <h1 style={{ fontSize: '48px', textAlign: 'center', color: 'darkblue' }}>
        INNOVA Services - SIMPLE TEST
      </h1>
      <p style={{ fontSize: '24px', textAlign: 'center', color: 'black' }}>
        Testing if component renders at all
      </p>
      <div style={{ backgroundColor: 'white', padding: '20px', margin: '20px', borderRadius: '10px' }}>
        <h2>Service 1: University Selection</h2>
        <p>Price: $299</p>
        <button style={{ backgroundColor: 'blue', color: 'white', padding: '10px 20px', border: 'none', borderRadius: '5px' }}>
          Learn More
        </button>
      </div>
    </div>
  );
};

export default Services;
