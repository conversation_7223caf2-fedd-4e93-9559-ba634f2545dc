import { useEffect, useState, useRef } from 'react';
import { CheckCircle, Users, Globe, Award, Star, BookOpen, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import useHeroStats from '../hooks/useHeroStats';
import { motion, useInView, useAnimation } from 'framer-motion';

const Hero = () => {
  const { stats, loading, error } = useHeroStats();
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const heroRef = useRef(null);
  const isInView = useInView(heroRef, { once: true });
  const controls = useAnimation();

  const testimonials = [
    { name: "<PERSON>", university: "Harvard University", text: "INNOVA made my dream come true!" },
    { name: "<PERSON>", university: "Oxford University", text: "Exceptional guidance throughout my journey." },
    { name: "<PERSON>", university: "MIT", text: "Professional and reliable service." }
  ];

  // Keep testimonial rotation for future testimonial carousel implementation
  console.log('Current testimonial:', testimonials[currentTestimonial]);

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}
      initial="hidden"
      animate={controls}
      variants={containerVariants}
    >
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0">
        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-400/30 to-purple-600/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute top-40 right-32 w-80 h-80 bg-gradient-to-r from-orange-400/30 to-pink-600/30 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-32 left-1/4 w-72 h-72 bg-gradient-to-r from-green-400/30 to-blue-600/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating geometric shapes */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-2 h-2 bg-white/20 rounded-full`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            className="text-center lg:text-left space-y-8"
            variants={itemVariants}
          >
            <motion.div className="space-y-6" variants={itemVariants}>
              <motion.div
                className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-4 border border-white/30"
                variants={itemVariants}
              >
                <Star className="h-4 w-4 mr-2" />
                #1 Study Abroad Consultancy
              </motion.div>

              <motion.h1
                className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight"
                variants={itemVariants}
              >
                Your Gateway to
                <span className="block mt-2 bg-gradient-to-r from-yellow-300 via-orange-300 to-pink-300 bg-clip-text text-transparent">
                  Global Education
                </span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-white/90 max-w-2xl mx-auto lg:mx-0 leading-relaxed font-medium"
                variants={itemVariants}
              >
                Transform your dreams into reality with expert guidance for studying abroad.
                We help students navigate their journey to world-class universities with personalized support and proven success.
              </motion.p>
            </motion.div>

            {/* Enhanced Features */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 gap-4"
              variants={itemVariants}
            >
              <motion.div
                className="flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30 hover:bg-white/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <CheckCircle className="text-green-300" size={20} />
                </div>
                <span className="font-semibold text-white">100% Visa Success Rate</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30 hover:bg-white/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Users className="text-blue-300" size={20} />
                </div>
                <span className="font-semibold text-white">Expert Counseling</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30 hover:bg-white/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="p-2 bg-orange-500/20 rounded-lg">
                  <Award className="text-orange-300" size={20} />
                </div>
                <span className="font-semibold text-white">Scholarship Assistance</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30 hover:bg-white/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Globe className="text-purple-300" size={20} />
                </div>
                <span className="font-semibold text-white">End-to-End Support</span>
              </motion.div>
            </motion.div>

            {/* Enhanced CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              variants={itemVariants}
            >
              <Link to="/contact">
                <motion.button
                  className="bg-white text-purple-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg group flex items-center justify-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Book Free Consultation
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </motion.button>
              </Link>
              <Link to="/services">
                <motion.button
                  className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-purple-600 transition-all duration-300 group flex items-center justify-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <BookOpen className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Explore Services
                </motion.button>
              </Link>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              className="flex flex-wrap items-center justify-center lg:justify-start gap-6 pt-8"
              variants={itemVariants}
            >
              <div className="text-sm text-white/80 font-medium">Trusted by students from</div>
              <div className="flex items-center space-x-4">
                <div className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-semibold text-white border border-white/30">IIT</div>
                <div className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-semibold text-white border border-white/30">NIT</div>
                <div className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-semibold text-white border border-white/30">DU</div>
                <div className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-semibold text-white border border-white/30">BHU</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Enhanced Visual Elements */}
          <motion.div
            className="relative"
            variants={itemVariants}
          >
            <div className="relative z-10">
              {/* Main Card with Enhanced Design */}
              <div className="card-elevated p-8 transform rotate-2 hover:rotate-0 transition-all duration-500 hover-lift">
                <div className="aspect-square bg-gradient-to-br from-blue-50 via-white to-orange-50 rounded-2xl flex items-center justify-center relative overflow-hidden">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 left-4 w-8 h-8 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-8 right-8 w-6 h-6 bg-orange-500 rotate-45"></div>
                    <div className="absolute bottom-8 left-8 w-4 h-4 bg-green-500 rounded-full"></div>
                    <div className="absolute bottom-4 right-4 w-10 h-10 bg-purple-500 rounded-full"></div>
                  </div>

                  <div className="text-center space-y-6 relative z-10">
                    <div className="relative">
                      <BookOpen className="h-20 w-20 text-blue-600 mx-auto" />
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <Star className="h-3 w-3 text-white" />
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">World-Class Education</h3>
                    <p className="text-gray-700 leading-relaxed font-medium">Access to top universities worldwide with comprehensive support</p>

                    {/* Enhanced Stats Display */}
                    {loading && (
                      <div className="flex justify-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    )}

                    {error && (
                      <p className="text-red-500 text-sm">Unable to load stats</p>
                    )}

                    {stats && (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-blue-600">{stats.students_placed}+</div>
                          <div className="text-gray-800 font-medium">Students</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-orange-600">{stats.countries}+</div>
                          <div className="text-gray-800 font-medium">Countries</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-green-600">{stats.success_rate}%</div>
                          <div className="text-gray-800 font-medium">Success</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-purple-600">{stats.years_experience}+</div>
                          <div className="text-gray-800 font-medium">Years</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Floating Cards */}
              <div className="absolute -top-6 -left-6 glass rounded-2xl p-6 transform -rotate-6 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-green-100 rounded-xl">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Expert Guidance</div>
                    <div className="text-sm text-gray-700 font-medium">Personalized counseling</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 glass rounded-2xl p-6 transform rotate-6 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-orange-100 rounded-xl">
                    <Award className="h-8 w-8 text-orange-500" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Proven Success</div>
                    <div className="text-sm text-gray-700 font-medium">High acceptance rates</div>
                  </div>
                </div>
              </div>

              <div className="absolute top-1/2 -right-10 glass rounded-2xl p-6 transform rotate-12 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-purple-100 rounded-xl">
                    <Globe className="h-8 w-8 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Global Network</div>
                    <div className="text-sm text-gray-700 font-medium">Worldwide connections</div>
                  </div>
                </div>
              </div>

              {/* Additional Floating Elements */}
              <div className="absolute top-1/4 -left-4 w-16 h-16 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute bottom-1/4 -right-4 w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full opacity-20 animate-pulse" style={{animationDelay: '1s'}}></div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, -10, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      >
        <div className="flex flex-col items-center space-y-2">
          <div className="text-xs text-white/80 font-semibold">Scroll to explore</div>
          <div className="w-6 h-10 border-2 border-white/60 rounded-full flex justify-center relative">
            <motion.div
              className="w-1 h-3 bg-gradient-to-b from-yellow-300 to-orange-300 rounded-full mt-2"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            />
          </div>
        </div>
      </motion.div>
    </motion.section>
  );
};

export default Hero;
